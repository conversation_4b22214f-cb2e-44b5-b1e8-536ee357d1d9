// 改进的餐厅菜单页面JavaScript文件
// 包含优化的addDishAction和redDishAction方法，点击+/-按钮后自动刷新页面

const { addToShoppingCart, subShoppingCart, getShoppingCart } = require('../../api/api.js');

Page({
  data: {
    // 菜品列表数据
    dishListItems: [],
    // 菜品详情数据
    dishDetailes: {},
    // 购物车数据
    orderAndUserInfo: [],
    // 其他页面数据...
    openDetailPop: false,
    openOrderCartList: false,
    loaddingSt: false,
    typeListData: [],
    typeIndex: 0,
    scrollTop: 0,
    itemId: '',
    shopStatus: 1,
    ht: 0,
    phoneData: {},
    moreNormdata: [],
    flavorDataes: [],
    moreNormDishdata: {},
    dishMealData: []
  },

  onLoad: function() {
    // 页面加载时初始化
    this.initPage();
  },

  onShow: function() {
    // 页面显示时刷新购物车数据
    this.refreshShoppingCart();
  },

  // 初始化页面
  initPage: function() {
    // 获取菜品分类数据
    this.getDishCategories();
    // 获取购物车数据
    this.refreshShoppingCart();
  },

  // 获取菜品分类数据
  getDishCategories: function() {
    // 这里应该调用获取菜品分类的API
    // 暂时使用模拟数据
    console.log('获取菜品分类数据');
  },

  // 刷新购物车数据
  refreshShoppingCart: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      return;
    }

    getShoppingCart().then((res) => {
      if (res.code === 1) {
        this.updateDishNumbers(res.data);
      }
    }).catch((err) => {
      console.error('获取购物车数据失败:', err);
    });
  },

  // 更新菜品数量显示
  updateDishNumbers: function(shoppingCartData) {
    const dishListItems = this.data.dishListItems;

    // 重置所有菜品数量
    dishListItems.forEach(dish => {
      dish.dishNumber = 0;
    });

    // 根据购物车数据更新菜品数量
    if (shoppingCartData && shoppingCartData.length > 0) {
      shoppingCartData.forEach(cartItem => {
        const dish = dishListItems.find(d =>
          (d.id === cartItem.dishId && cartItem.dishId) ||
          (d.id === cartItem.setmealId && cartItem.setmealId)
        );
        if (dish) {
          dish.dishNumber = (dish.dishNumber || 0) + cartItem.number;
        }
      });
    }

    this.setData({
      dishListItems: dishListItems
    });
  },

  // 改进的加入购物车方法
  addDishAction: function(e, type) {
    // 获取菜品数据
    let dish;
    if (type === '购物车') {
      // 从购物车列表中获取菜品数据
      const dataset = e.currentTarget.dataset;
      dish = this.getDishFromCartList(dataset);
    } else {
      // 从菜品列表中获取菜品数据
      dish = this.getDishFromEvent(e);
    }

    if (!dish) {
      wx.showToast({
        title: '获取菜品信息失败',
        icon: 'error'
      });
      return;
    }

    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      });
      this.doWechatLogin();
      return;
    }

    // 构建购物车数据
    const cartData = {
      dishId: dish.type === 1 ? dish.id : null,
      setmealId: dish.type === 2 ? dish.id : null,
      dishFlavor: dish.dishFlavor || null
    };

    // 调用后端API加入购物车
    addToShoppingCart(cartData).then((res) => {
      if (res.code === 1) {
        wx.showToast({
          title: '已加入购物车',
          icon: 'success'
        });

        // 立即更新菜品数量显示 +1
        this.updateDishNumberInList(dish.id, dish.type, 1);

        // 刷新购物车数据以确保数据一致性
        this.refreshShoppingCart();

        // 自动刷新页面
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }, 500);
      } else {
        wx.showToast({
          title: res.msg || '加入购物车失败',
          icon: 'error'
        });
      }
    }).catch((err) => {
      console.error('加入购物车失败:', err);
      wx.showToast({
        title: '加入购物车失败',
        icon: 'error'
      });
    });
  },

  // 改进的减少菜品数量方法
  redDishAction: function(e, type) {
    // 获取菜品数据
    let dish;
    if (type === '购物车') {
      // 从购物车列表中获取菜品数据
      const dataset = e.currentTarget.dataset;
      dish = this.getDishFromCartList(dataset);
    } else {
      // 从菜品列表中获取菜品数据
      dish = this.getDishFromEvent(e);
    }

    if (!dish) {
      wx.showToast({
        title: '获取菜品信息失败',
        icon: 'error'
      });
      return;
    }

    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      });
      this.doWechatLogin();
      return;
    }

    // 检查菜品数量
    if (!dish.dishNumber || dish.dishNumber <= 0) {
      wx.showToast({
        title: '菜品数量不能为负数',
        icon: 'none'
      });
      return;
    }

    // 构建购物车数据
    const cartData = {
      dishId: dish.type === 1 ? dish.id : null,
      setmealId: dish.type === 2 ? dish.id : null,
      dishFlavor: dish.dishFlavor || null
    };

    // 调用后端API减少购物车商品
    subShoppingCart(cartData).then((res) => {
      if (res.code === 1) {
        wx.showToast({
          title: '已从购物车移除',
          icon: 'success'
        });

        // 立即更新菜品数量显示 -1
        this.updateDishNumberInList(dish.id, dish.type, -1);

        // 刷新购物车数据以确保数据一致性
        this.refreshShoppingCart();

        // 自动刷新页面
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }, 500);
      } else {
        wx.showToast({
          title: res.msg || '操作失败',
          icon: 'error'
        });
      }
    }).catch((err) => {
      console.error('减少购物车商品失败:', err);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    });
  },

  // 立即更新菜品数量显示（用于快速响应用户操作）
  updateDishNumberInList: function(dishId, dishType, change) {
    const dishListItems = [...this.data.dishListItems];
    const dishDetailes = { ...this.data.dishDetailes };

    // 更新菜品列表中的数量
    const dishIndex = dishListItems.findIndex(d => d.id === dishId);
    if (dishIndex !== -1) {
      dishListItems[dishIndex].dishNumber = Math.max(0, (dishListItems[dishIndex].dishNumber || 0) + change);
    }

    // 更新菜品详情中的数量（如果是同一个菜品）
    if (dishDetailes.id === dishId) {
      dishDetailes.dishNumber = Math.max(0, (dishDetailes.dishNumber || 0) + change);
    }

    this.setData({
      dishListItems: dishListItems,
      dishDetailes: dishDetailes
    });
  },

  // 从事件中获取菜品数据
  getDishFromEvent: function(e) {
    const dataset = e.currentTarget.dataset;

    // 如果是从菜品详情弹窗操作
    if (this.data.dishDetailes && this.data.dishDetailes.id) {
      return this.data.dishDetailes;
    }

    // 如果是从菜品列表操作，需要根据索引获取
    if (dataset && typeof dataset.index !== 'undefined') {
      const index = parseInt(dataset.index);
      if (this.data.dishListItems && this.data.dishListItems[index]) {
        return this.data.dishListItems[index];
      }
    }

    return null;
  },

  // 从购物车列表中获取菜品数据
  getDishFromCartList: function(dataset) {
    if (!dataset || typeof dataset.ind === 'undefined' || typeof dataset.index === 'undefined') {
      return null;
    }

    const ind = parseInt(dataset.ind);
    const index = parseInt(dataset.index);

    if (this.data.orderAndUserInfo &&
        this.data.orderAndUserInfo[ind] &&
        this.data.orderAndUserInfo[ind].dishList &&
        this.data.orderAndUserInfo[ind].dishList[index]) {
      return this.data.orderAndUserInfo[ind].dishList[index];
    }

    return null;
  },

  // 微信登录
  doWechatLogin: function() {
    wx.login({
      success: (res) => {
        if (res.code) {
          // 调用登录API
          this.callLoginAPI(res.code);
        } else {
          console.error('登录失败！' + res.errMsg);
        }
      }
    });
  },

  // 调用登录API
  callLoginAPI: function(code) {
    const { userLogin } = require('../../api/api.js');

    userLogin(code).then((res) => {
      if (res.code === 1) {
        // 保存token
        wx.setStorageSync('token', res.data.token);
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
        // 刷新购物车数据
        this.refreshShoppingCart();
      } else {
        wx.showToast({
          title: res.msg || '登录失败',
          icon: 'error'
        });
      }
    }).catch((err) => {
      console.error('登录失败:', err);
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      });
    });
  }
});
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=57280228&scoped=true& */ 17);
/* harmony import */ var _index_js_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! .?vue&type=script&lang=js& */ 19);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_js_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_js_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _style_scss_vue_type_style_index_0_id_57280228_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./style.scss?vue&type=style&index=0&id=57280228&lang=scss&scoped=true& */ 30);
/* harmony import */ var _index_vue_vue_type_style_index_1_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=1&id=57280228&scoped=true&lang=css& */ 32);
/* harmony import */ var _F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 11);

var renderjs






/* normalize component */

var component = Object(_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_4__["default"])(
  _index_js_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "57280228",
  null,
  false,
  _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/index/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 17:
/*!**************************************************************************************************************!*\
  !*** D:/project/project-rjwm-weixin-uniapp/pages/index/index.vue?vue&type=template&id=57280228&scoped=true& ***!
  \**************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_16_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--16-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true& */ 18);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_16_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_16_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_16_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_16_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 18:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--16-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/project/project-rjwm-weixin-uniapp/pages/index/index.vue?vue&type=template&id=57280228&scoped=true& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function() {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 =
    _vm.dishListItems && _vm.dishListItems.length > 0
      ? _vm.__map(_vm.dishListItems, function(item, index) {
          var $orig = _vm.__get_orig(item)

          var g0 = item.price.toFixed(2)
          return {
            $orig: $orig,
            g0: g0
          }
        })
      : null
  var m0 = _vm.orderListData()
  var g1 = !(m0.length === 0) ? _vm.orderDishPrice.toFixed(2) : null

  var l2 = _vm.__map(_vm.moreNormdata, function(obj, index) {
    var $orig = _vm.__get_orig(obj)

    var l1 = _vm.__map(obj.value, function(item, ind) {
      var $orig = _vm.__get_orig(item)

      var g2 = _vm.flavorDataes.findIndex(function(it) {
        return item === it
      })
      return {
        $orig: $orig,
        g2: g2
      }
    })

    return {
      $orig: $orig,
      l1: l1
    }
  })

  var g3 = _vm.dishDetailes.type == 1 ? _vm.dishDetailes.price.toFixed(2) : null

  if (!_vm._isMounted) {
    _vm.e0 = function() {
      return (_vm.openOrderCartList = !_vm.openOrderCartList)
    }

    _vm.e1 = function() {
      return (_vm.openDetailPop = false)
    }

    _vm.e2 = function() {
      return (_vm.openDetailPop = false)
    }

    _vm.e3 = function($event) {
      _vm.openOrderCartList = !_vm.openOrderCartList
    }

    _vm.e4 = function($event) {
      $event.stopPropagation()
      _vm.openOrderCartList = _vm.openOrderCartList
    }
  }

  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
        m0: m0,
        g1: g1,
        l2: l2,
        g3: g3
      }
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 32:
/*!****************************************************************************************************************************!*\
  !*** D:/project/project-rjwm-weixin-uniapp/pages/index/index.vue?vue&type=style&index=1&id=57280228&scoped=true&lang=css& ***!
  \****************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=57280228&scoped=true&lang=css& */ 33);
/* harmony import */ var _F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_F_HBuilderX_2_2_2_20190816_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 33:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/project/project-rjwm-weixin-uniapp/pages/index/index.vue?vue&type=style&index=1&id=57280228&scoped=true&lang=css& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[15,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map