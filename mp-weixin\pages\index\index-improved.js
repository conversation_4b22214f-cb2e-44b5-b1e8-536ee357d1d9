// 改进的餐厅菜单页面JavaScript文件
// 包含优化的addDishAction和redDishAction方法

const { addToShoppingCart, subShoppingCart, getShoppingCart } = require('../../api/api.js');

Page({
  data: {
    // 菜品列表数据
    dishListItems: [],
    // 菜品详情数据
    dishDetailes: {},
    // 购物车数据
    orderAndUserInfo: [],
    // 其他页面数据...
    openDetailPop: false,
    openOrderCartList: false,
    loaddingSt: false,
    typeListData: [],
    typeIndex: 0,
    scrollTop: 0,
    itemId: '',
    shopStatus: 1,
    ht: 0,
    phoneData: {},
    moreNormdata: [],
    flavorDataes: [],
    moreNormDishdata: {},
    dishMealData: []
  },

  onLoad: function() {
    // 页面加载时初始化
    this.initPage();
  },

  onShow: function() {
    // 页面显示时刷新购物车数据
    this.refreshShoppingCart();
  },

  // 初始化页面
  initPage: function() {
    // 获取菜品分类数据
    this.getDishCategories();
    // 获取购物车数据
    this.refreshShoppingCart();
  },

  // 获取菜品分类数据
  getDishCategories: function() {
    // 这里应该调用获取菜品分类的API
    // 暂时使用模拟数据
    console.log('获取菜品分类数据');
  },

  // 刷新购物车数据
  refreshShoppingCart: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      return;
    }

    getShoppingCart().then((res) => {
      if (res.code === 1) {
        this.updateDishNumbers(res.data);
      }
    }).catch((err) => {
      console.error('获取购物车数据失败:', err);
    });
  },

  // 更新菜品数量显示
  updateDishNumbers: function(shoppingCartData) {
    const dishListItems = this.data.dishListItems;
    
    // 重置所有菜品数量
    dishListItems.forEach(dish => {
      dish.dishNumber = 0;
    });

    // 根据购物车数据更新菜品数量
    if (shoppingCartData && shoppingCartData.length > 0) {
      shoppingCartData.forEach(cartItem => {
        const dish = dishListItems.find(d => 
          (d.id === cartItem.dishId && cartItem.dishId) || 
          (d.id === cartItem.setmealId && cartItem.setmealId)
        );
        if (dish) {
          dish.dishNumber = (dish.dishNumber || 0) + cartItem.number;
        }
      });
    }

    this.setData({
      dishListItems: dishListItems
    });
  },

  // 改进的加入购物车方法
  addDishAction: function(e, type) {
    // 获取菜品数据
    let dish;
    if (type === '购物车') {
      // 从购物车列表中获取菜品数据
      const dataset = e.currentTarget.dataset;
      dish = this.getDishFromCartList(dataset);
    } else {
      // 从菜品列表中获取菜品数据
      dish = this.getDishFromEvent(e);
    }

    if (!dish) {
      wx.showToast({
        title: '获取菜品信息失败',
        icon: 'error'
      });
      return;
    }

    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      });
      this.doWechatLogin();
      return;
    }

    // 构建购物车数据
    const cartData = {
      dishId: dish.type === 1 ? dish.id : null,
      setmealId: dish.type === 2 ? dish.id : null,
      dishFlavor: dish.dishFlavor || null
    };

    // 调用后端API加入购物车
    addToShoppingCart(cartData).then((res) => {
      if (res.code === 1) {
        wx.showToast({
          title: '已加入购物车',
          icon: 'success'
        });

        // 立即更新菜品数量显示 +1
        this.updateDishNumberInList(dish.id, dish.type, 1);

        // 刷新购物车数据以确保数据一致性
        this.refreshShoppingCart();

        // 自动刷新页面
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }, 500);
      } else {
        wx.showToast({
          title: res.msg || '加入购物车失败',
          icon: 'error'
        });
      }
    }).catch((err) => {
      console.error('加入购物车失败:', err);
      wx.showToast({
        title: '加入购物车失败',
        icon: 'error'
      });
    });
  },

  // 改进的减少菜品数量方法
  redDishAction: function(e, type) {
    // 获取菜品数据
    let dish;
    if (type === '购物车') {
      // 从购物车列表中获取菜品数据
      const dataset = e.currentTarget.dataset;
      dish = this.getDishFromCartList(dataset);
    } else {
      // 从菜品列表中获取菜品数据
      dish = this.getDishFromEvent(e);
    }

    if (!dish) {
      wx.showToast({
        title: '获取菜品信息失败',
        icon: 'error'
      });
      return;
    }

    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      });
      this.doWechatLogin();
      return;
    }

    // 检查菜品数量
    if (!dish.dishNumber || dish.dishNumber <= 0) {
      wx.showToast({
        title: '菜品数量不能为负数',
        icon: 'none'
      });
      return;
    }

    // 构建购物车数据
    const cartData = {
      dishId: dish.type === 1 ? dish.id : null,
      setmealId: dish.type === 2 ? dish.id : null,
      dishFlavor: dish.dishFlavor || null
    };

    // 调用后端API减少购物车商品
    subShoppingCart(cartData).then((res) => {
      if (res.code === 1) {
        wx.showToast({
          title: '已从购物车移除',
          icon: 'success'
        });

        // 立即更新菜品数量显示 -1
        this.updateDishNumberInList(dish.id, dish.type, -1);

        // 刷新购物车数据以确保数据一致性
        this.refreshShoppingCart();

        // 自动刷新页面
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }, 500);
      } else {
        wx.showToast({
          title: res.msg || '操作失败',
          icon: 'error'
        });
      }
    }).catch((err) => {
      console.error('减少购物车商品失败:', err);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    });
  },

  // 立即更新菜品数量显示（用于快速响应用户操作）
  updateDishNumberInList: function(dishId, dishType, change) {
    const dishListItems = [...this.data.dishListItems];
    const dishDetailes = { ...this.data.dishDetailes };
    
    // 更新菜品列表中的数量
    const dishIndex = dishListItems.findIndex(d => d.id === dishId);
    if (dishIndex !== -1) {
      dishListItems[dishIndex].dishNumber = Math.max(0, (dishListItems[dishIndex].dishNumber || 0) + change);
    }
    
    // 更新菜品详情中的数量（如果是同一个菜品）
    if (dishDetailes.id === dishId) {
      dishDetailes.dishNumber = Math.max(0, (dishDetailes.dishNumber || 0) + change);
    }
    
    this.setData({
      dishListItems: dishListItems,
      dishDetailes: dishDetailes
    });
  },

  // 从事件中获取菜品数据
  getDishFromEvent: function(e) {
    const dataset = e.currentTarget.dataset;

    // 如果是从菜品详情弹窗操作
    if (this.data.dishDetailes && this.data.dishDetailes.id) {
      return this.data.dishDetailes;
    }

    // 如果是从菜品列表操作，需要根据索引获取
    if (dataset && typeof dataset.index !== 'undefined') {
      const index = parseInt(dataset.index);
      if (this.data.dishListItems && this.data.dishListItems[index]) {
        return this.data.dishListItems[index];
      }
    }

    return null;
  },

  // 从购物车列表中获取菜品数据
  getDishFromCartList: function(dataset) {
    if (!dataset || typeof dataset.ind === 'undefined' || typeof dataset.index === 'undefined') {
      return null;
    }

    const ind = parseInt(dataset.ind);
    const index = parseInt(dataset.index);

    if (this.data.orderAndUserInfo &&
        this.data.orderAndUserInfo[ind] &&
        this.data.orderAndUserInfo[ind].dishList &&
        this.data.orderAndUserInfo[ind].dishList[index]) {
      return this.data.orderAndUserInfo[ind].dishList[index];
    }

    return null;
  },

  // 微信登录
  doWechatLogin: function() {
    wx.login({
      success: (res) => {
        if (res.code) {
          // 调用登录API
          this.callLoginAPI(res.code);
        } else {
          console.error('登录失败！' + res.errMsg);
        }
      }
    });
  },

  // 调用登录API
  callLoginAPI: function(code) {
    const { userLogin } = require('../../api/api.js');

    userLogin(code).then((res) => {
      if (res.code === 1) {
        // 保存token
        wx.setStorageSync('token', res.data.token);
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
        // 刷新购物车数据
        this.refreshShoppingCart();
      } else {
        wx.showToast({
          title: res.msg || '登录失败',
          icon: 'error'
        });
      }
    }).catch((err) => {
      console.error('登录失败:', err);
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      });
    });
  },

  // 打开菜品详情
  openDetailHandle: function(e) {
    const dish = this.getDishFromEvent(e);
    if (dish) {
      this.setData({
        dishDetailes: dish,
        openDetailPop: true
      });
    }
  },

  // 关闭菜品详情
  closeDetailPop: function() {
    this.setData({
      openDetailPop: false
    });
  },

  // 切换购物车显示
  toggleOrderCartList: function() {
    this.setData({
      openOrderCartList: !this.data.openOrderCartList
    });
  },

  // 清空购物车
  clearCardOrder: function() {
    wx.showModal({
      title: '提示',
      content: '确定要清空购物车吗？',
      success: (res) => {
        if (res.confirm) {
          this.clearShoppingCart();
        }
      }
    });
  },

  // 调用清空购物车API
  clearShoppingCart: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      });
      return;
    }

    // 这里需要调用清空购物车的API
    wx.request({
      url: 'http://localhost:8080/user/shoppingCart/clean',
      method: 'DELETE',
      header: {
        'Content-Type': 'application/json',
        'authentication': token
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.code === 1) {
          wx.showToast({
            title: '购物车已清空',
            icon: 'success'
          });
          // 刷新页面数据
          this.refreshShoppingCart();
          this.setData({
            openOrderCartList: false
          });
        } else {
          wx.showToast({
            title: res.data.msg || '清空失败',
            icon: 'error'
          });
        }
      },
      fail: (err) => {
        console.error('清空购物车失败:', err);
        wx.showToast({
          title: '清空失败',
          icon: 'error'
        });
      }
    });
  },

  // 禁用滚动
  disabledScroll: function() {
    return false;
  },

  // 切换菜单分类
  swichMenu: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      typeIndex: index
    });
    // 这里可以添加获取对应分类菜品的逻辑
  },

  // 处理规格选择
  moreNormDataesHandle: function(e) {
    const dish = this.getDishFromEvent(e);
    if (dish && dish.flavors && dish.flavors.length > 0) {
      // 处理有规格的菜品
      this.setData({
        moreNormDishdata: dish,
        // 显示规格选择弹窗的逻辑
      });
    }
  },

  // 添加带规格的菜品到购物车
  addShop: function(e) {
    const dish = this.data.moreNormDishdata;
    if (!dish) {
      return;
    }

    // 获取选中的规格
    const selectedFlavors = this.getSelectedFlavors();

    // 构建购物车数据
    const cartData = {
      dishId: dish.type === 1 ? dish.id : null,
      setmealId: dish.type === 2 ? dish.id : null,
      dishFlavor: selectedFlavors
    };

    // 调用加入购物车API
    addToShoppingCart(cartData).then((res) => {
      if (res.code === 1) {
        wx.showToast({
          title: '已加入购物车',
          icon: 'success'
        });
        // 更新数量显示
        this.updateDishNumberInList(dish.id, dish.type, 1);
        this.refreshShoppingCart();

        // 自动刷新页面
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }, 500);
      } else {
        wx.showToast({
          title: res.msg || '加入购物车失败',
          icon: 'error'
        });
      }
    }).catch((err) => {
      console.error('加入购物车失败:', err);
      wx.showToast({
        title: '加入购物车失败',
        icon: 'error'
      });
    });
  },

  // 获取选中的规格
  getSelectedFlavors: function() {
    // 这里需要根据实际的规格选择逻辑来实现
    return '';
  }
});
